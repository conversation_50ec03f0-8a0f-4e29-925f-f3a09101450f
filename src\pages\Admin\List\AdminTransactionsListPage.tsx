import React, { useCallback, useEffect, useState, ChangeEvent } from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";
import MkdInputV2 from "../../../components/MkdInputV2";
import { useNavigate } from "react-router-dom";
import { useSDK } from "../../../hooks/useSDK";
import { Skeleton } from "../../../components/Skeleton";
import { PaginationBar } from "../../../components/PaginationBar";

interface Transaction {
  id: string;
  user: string;
  type: string;
  amount: string;
  fee: string;
  status: string;
  date: string;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

const AdminTransactionsListPage = () => {
  const navigate = useNavigate();
  const { sdk } = useSDK();

  // Mock data to match the uploaded image
  const mockTransactions: Transaction[] = [
    {
      id: "TRX-12345",
      user: "<PERSON>",
      type: "Buy",
      amount: "eBa$ 200",
      fee: "eBa$ 10",
      status: "Completed",
      date: "2025-04-23",
    },
    {
      id: "TRX-12346",
      user: "Sarah Johnson",
      type: "Sell",
      amount: "eBa$ 150",
      fee: "eBa$ 5",
      status: "Completed",
      date: "2025-04-23",
    },
    {
      id: "TRX-12347",
      user: "Mike Wilson",
      type: "Buy",
      amount: "eBa$ 75",
      fee: "eBa$ 3",
      status: "Completed",
      date: "2025-04-22",
    },
    {
      id: "TRX-12348",
      user: "Emma Davis",
      type: "Sell",
      amount: "eBa$ 425",
      fee: "eBa$ 15",
      status: "Completed",
      date: "2025-04-22",
    },
    {
      id: "TRX-12349",
      user: "David Brown",
      type: "Buy",
      amount: "eBa$ 200",
      fee: "eBa$ 8",
      status: "Completed",
      date: "2025-04-21",
    },
    {
      id: "TRX-12350",
      user: "Lisa Anderson",
      type: "Sell",
      amount: "eBa$ 350",
      fee: "eBa$ 12",
      status: "Completed",
      date: "2025-04-21",
    },
    {
      id: "TRX-12351",
      user: "Robert Taylor",
      type: "Buy",
      amount: "eBa$ 125",
      fee: "eBa$ 4",
      status: "Completed",
      date: "2025-04-20",
    },
  ];

  const [transactions, setTransactions] =
    useState<Transaction[]>(mockTransactions);
  const [pagination, setPagination] = useState<Pagination | null>({
    page: 1,
    limit: 10,
    total: 247,
    num_pages: 25,
    has_next: true,
    has_prev: false,
  });
  const [loading, setLoading] = useState(false);

  const [uiFilters, setUiFilters] = useState({
    search: "",
    status: "all",
    range: "all",
    type: "all",
  });

  const [apiParams, setApiParams] = useState({
    search: "",
    status: "all",
    range: "all",
    type: "all",
    page: 1,
    limit: 10,
  });

  const fetchTransactions = useCallback(async () => {
    setLoading(true);
    try {
      console.log("Fetching transactions with params:", apiParams);
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/transactions`,
        method: "GET",
        params: apiParams,
      });

      console.log("Transactions API response:", response);
      if (!response.error) {
        console.log("Setting transactions data:", response.data);
        console.log("Setting pagination:", (response as any).pagination);
        setTransactions(response.data);
        setPagination((response as any).pagination);
      } else {
        console.error("API returned error:", response.error, response.message);
        // If API fails, fall back to mock data for now
        console.log("Falling back to mock data");
        setTransactions(mockTransactions);
      }
    } catch (error: any) {
      console.error("Error fetching transactions:", error);
      // If API fails, fall back to mock data for now
      console.log("Falling back to mock data due to error");
      setTransactions(mockTransactions);
    } finally {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [apiParams]);

  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);

  const handleFilterInputChange = (key: string, value: string | number) => {
    setUiFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    setApiParams({ ...apiParams, ...uiFilters, page: 1 });
  };

  const handlePageChange = (newPage: number) => {
    setApiParams((prev) => ({ ...prev, page: newPage }));
  };

  const handleLimitChange = (newLimit: number) => {
    setApiParams((prev) => ({ ...prev, limit: newLimit, page: 1 }));
  };

  const statusOptions = [
    { value: "all", label: "All Status" },
    { value: "completed", label: "Completed" },
    { value: "pending", label: "Pending" },
    { value: "failed", label: "Failed" },
    { value: "cancelled", label: "Cancelled" },
  ];

  const typeOptions = [
    { value: "all", label: "All Types" },
    { value: "buy", label: "Buy" },
    { value: "sell", label: "Sell" },
    { value: "top-up", label: "Top-up" },
    { value: "withdrawal", label: "Withdrawal" },
  ];

  const rangeOptions = [
    { value: "all", label: "All Time" },
    { value: "7days", label: "Last 7 Days" },
    { value: "30days", label: "Last 30 Days" },
  ];
  return (
    <AdminWrapper>
      <div className="bg-gray-50 min-h-screen">
        <div className="flex justify-between items-start p-6 pb-0">
          <div>
            <h1 className="text-3xl font-bold text-[#1E293B]">
              Transactions Management
            </h1>
            <p className="text-gray-600 mt-1 text-sm">
              Track and audit platform transactions
            </p>
          </div>
          <div className="flex items-center">
            <img
              src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
              alt="User avatar"
              className="h-8 w-8 rounded-full"
            />
          </div>
        </div>
        <div className="bg-white mx-6 mb-6 rounded-lg shadow-sm">
          <div className="p-6">
            <div className="flex items-end gap-4">
              <div className="flex-[2]">
                <label
                  htmlFor="search"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Search
                </label>
                <MkdInputV2
                  value={uiFilters.search}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    handleFilterInputChange("search", e.target.value)
                  }
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Field
                      placeholder="Search transactions..."
                      className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
              <div className="flex-[1]">
                <label
                  htmlFor="status"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Status
                </label>
                <MkdInputV2
                  type="mapping"
                  mapping={statusOptions.reduce(
                    (acc, curr) => ({ ...acc, [curr.value]: curr.label }),
                    {}
                  )}
                  value={uiFilters.status}
                  onChange={(e: ChangeEvent<HTMLSelectElement>) =>
                    handleFilterInputChange("status", e.target.value)
                  }
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Field className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
              <div className="flex-[1]">
                <label
                  htmlFor="range"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Range
                </label>
                <MkdInputV2
                  type="mapping"
                  mapping={rangeOptions.reduce(
                    (acc, curr) => ({ ...acc, [curr.value]: curr.label }),
                    {}
                  )}
                  value={uiFilters.range}
                  onChange={(e: ChangeEvent<HTMLSelectElement>) =>
                    handleFilterInputChange("range", e.target.value)
                  }
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Field className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
              <div className=" h-full ">
                <InteractiveButton
                  onClick={handleApplyFilters}
                  className="!bg-gray-900 !hover:bg-gray-800 text-white  px-6 !py-4 rounded-md font-medium !h-full"
                >
                  Apply filters
                </InteractiveButton>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white mx-6 rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Transaction ID
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    User
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Type
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Amount
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Fee
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Date
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <tr key={index}>
                      <td colSpan={8} className="px-6 py-4">
                        <Skeleton className="h-4 w-full" />
                      </td>
                    </tr>
                  ))
                ) : transactions.length > 0 ? (
                  transactions.filter((transaction) => transaction.type !== "top_up").map((transaction) => (
                    <tr key={transaction.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {transaction.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.user}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            transaction.type === "Buy"
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {transaction.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {transaction.amount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.fee}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            transaction.status === "Completed"
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {transaction.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() =>
                            navigate(
                              `/admin/view-transaction/${transaction.id}`
                            )
                          }
                          className="text-blue-600 hover:text-blue-900"
                        >
                          View
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={8} className="text-center py-10 text-gray-500">
                      No transactions found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          {pagination && pagination.total > 0 && (
            <div className="bg-white px-6 py-3 flex items-center justify-between border-t border-gray-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.has_prev}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.has_next}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{" "}
                    <span className="font-medium">
                      {(pagination.page - 1) * pagination.limit + 1}
                    </span>{" "}
                    to{" "}
                    <span className="font-medium">
                      {Math.min(
                        pagination.page * pagination.limit,
                        pagination.total
                      )}
                    </span>{" "}
                    of <span className="font-medium">{pagination.total}</span>{" "}
                    transactions
                  </p>
                </div>
                <div>
                  <nav
                    className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                    aria-label="Pagination"
                  >
                    <button
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={!pagination.has_prev}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">Previous</span>
                      Previous
                    </button>

                    {/* Page numbers */}
                    {Array.from(
                      { length: Math.min(5, pagination.num_pages) },
                      (_, i) => {
                        const pageNum = i + 1;
                        return (
                          <button
                            key={pageNum}
                            onClick={() => handlePageChange(pageNum)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              pageNum === pagination.page
                                ? "z-10 bg-red-500 border-red-500 text-white"
                                : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      }
                    )}

                    <button
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={!pagination.has_next}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">Next</span>
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminTransactionsListPage;
